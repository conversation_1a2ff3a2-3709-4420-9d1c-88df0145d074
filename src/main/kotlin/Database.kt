package com.gulderbone

import models.data.Items
import models.data.Orders
import models.data.OrderItems
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SchemaUtils
import org.jetbrains.exposed.sql.transactions.transaction

fun configureDatabase() {
    // Use environment variables to determine database connection details
    val dbHost = System.getenv("PGHOST") ?: "localhost"
    val dbPort = System.getenv("PGPORT") ?: "5432"
    val dbName = System.getenv("PGDATABASE") ?: "coffee_db"
    val dbUser = System.getenv("PGUSER") ?: "postgres"
    val dbPassword = System.getenv("PGPASSWORD") ?: "postgres"

    val databaseUrl = "*****************************************"
    val driver = "org.postgresql.Driver"

    Database.connect(
        url = databaseUrl,
        driver = driver,
        user = dbUser,
        password = dbPassword
    )

    transaction {
        SchemaUtils.create(Items, Orders, OrderItems)
    }
}
