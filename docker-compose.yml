services:
  postgres:
    image: postgres:15
    container_name: coffee-postgres
    environment:
      PGHOST: ${PGHOST}
      PGPORT: ${PGPORT}
      PGUSER: ${PGUSER}
      PGPASSWORD: ${PGPASSWORD}
      PGDATABASE: ${PGDATABASE}
      DATABASE_URL: ${DATABASE_URL}
      PGDATA: ${PGDATA}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - coffee-network

  coffee-server:
    build: .
    container_name: coffee-server
    depends_on:
      - postgres
    environment:
      USE_POSTGRES: "true"
      PGHOST: ${PGHOST}
      PGPORT: ${PGPORT}
      PGUSER: ${PGUSER}
      PGPASSWORD: ${PGPASSWORD}
      PGDATABASE: ${PGDATABASE}
      PORT: ${PORT}
    ports:
      - "${PORT:-8080}:8080"
    networks:
      - coffee-network

networks:
  coffee-network:
    driver: bridge

volumes:
  postgres_data:
